<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bank Webhook Simulator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            background-color: #f5f7fb; 
            color: #333;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .header { 
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%); 
            color: white; 
            padding: 2rem 0; 
            margin-bottom: 2rem;
        }
        .card { 
            border-radius: 10px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
            margin-bottom: 20px; 
            border: none;
        }
        .card-header { 
            background-color: #f8f9fa; 
            font-weight: 600; 
            border-bottom: 1px solid #eaecef;
        }
        .btn-primary { 
            background-color: #3498db; 
            border: none; 
            padding: 10px 20px;
        }
        .btn-primary:hover { 
            background-color: #2980b9; 
        }
        .webhook-payload { 
            background-color: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px; 
            max-height: 300px; 
            overflow-y: auto; 
            font-family: monospace;
            font-size: 14px;
        }
        .history-item { 
            border-left: 4px solid #3498db; 
            padding-left: 15px; 
            margin-bottom: 15px; 
        }
        .email-badge { 
            font-size: 0.8rem; 
            padding: 0.35em 0.65em; 
        }
        .config-section {
            background-color: #e8f4fc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .feature-icon {
            font-size: 2rem;
            color: #3498db;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="header text-center">
        <div class="container">
            <h1><i class="fas fa-exchange-alt me-2"></i>Bank Webhook Simulator</h1>
            <p class="lead">Generate realistic bank webhook payloads for testing and development</p>
        </div>
    </div>

    <div class="container">
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="card h-100">
                    <div class="card-body">
                        <i class="fas fa-globe feature-icon"></i>
                        <h5>Multiple Bank Regions</h5>
                        <p>Simulate webhooks from banks in US, EU, UK, Australia, and Japan</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="card h-100">
                    <div class="card-body">
                        <i class="fas fa-envelope feature-icon"></i>
                        <h5>Email Integration</h5>
                        <p>Send generated webhooks directly to email addresses</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="card h-100">
                    <div class="card-body">
                        <i class="fas fa-code feature-icon"></i>
                        <h5>Realistic Data</h5>
                        <p>Generate payloads with realistic transaction data and formats</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cog me-2"></i>Webhook Configuration
                    </div>
                    <div class="card-body">
                        <div class="config-section">
                            <h5><i class="fas fa-sliders-h me-2"></i>Settings</h5>
                            <form id="webhookForm">
                                <div class="mb-3">
                                    <label for="country" class="form-label">Bank Country</label>
                                    <select class="form-select" id="country" name="country">
                                        <option value="US">US Global Bank</option>
                                        <option value="EU">European Union Bank</option>
                                        <option value="UK">British Financial Group</option>
                                        <option value="AU">Australia Pacific Bank</option>
                                        <option value="JP">Japan International Bank</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="event_type" class="form-label">Event Type</label>
                                    <select class="form-select" id="event_type" name="event_type">
                                        <option value="payment.completed">Payment Completed</option>
                                        <option value="payment.failed">Payment Failed</option>
                                        <option value="transfer.completed">Transfer Completed</option>
                                        <option value="refund.processed">Refund Processed</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="api_version" class="form-label">API Version</label>
                                    <input type="text" class="form-control" id="api_version" name="api_version" value="2023-08-01">
                                </div>
                            </form>
                        </div>

                        <div class="config-section">
                            <h5><i class="fas fa-paper-plane me-2"></i>Email Options</h5>
                            <div class="mb-3 form-check form-switch">
                                <input type="checkbox" class="form-check-input" id="send_email" name="send_email">
                                <label class="form-check-label" for="send_email">Send via Email</label>
                            </div>
                            
                            <div class="mb-3" id="emailField" style="display: none;">
                                <label for="recipient_email" class="form-label">Recipient Email</label>
                                <input type="email" class="form-control" id="recipient_email" name="recipient_email" placeholder="Enter email address">
                            </div>
                        </div>
                        
                        <button id="generateBtn" class="btn btn-primary w-100">
                            <i class="fas fa-bolt me-2"></i>Generate Webhook
                        </button>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-history me-2"></i>Recent Webhooks
                    </div>
                    <div class="card-body">
                        <div id="historyList"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-code me-2"></i>Generated Payload</span>
                        <button id="copyBtn" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-copy me-1"></i>Copy
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info" id="emailStatus" style="display: none;"></div>
                        <pre id="payloadDisplay" class="webhook-payload">{"status": "Click generate to create a webhook"}</pre>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-info-circle me-2"></i>How It Works
                    </div>
                    <div class="card-body">
                        <p>This tool generates realistic bank webhook payloads for testing your applications.</p>
                        <ol>
                            <li>Select a bank region and event type</li>
                            <li>Optionally provide an email address to send the payload</li>
                            <li>Click "Generate Webhook" to create a payload</li>
                            <li>Use the copy button to copy the JSON to your clipboard</li>
                        </ol>
                        <p class="mb-0"><strong>Note:</strong> Email functionality requires a server-side implementation. The current implementation simulates email sending.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light text-center p-4 mt-5">
        <p class="mb-0">Bank Webhook Simulator &copy; 2023 - For testing and development purposes only</p>
    </footer>

    <script>
        // Bank templates for different regions
        const BANK_TEMPLATES = {
            "US": {
                "name": "US Global Bank",
                "currencies": ["USD"],
                "account_format": "XXXXXXX#######",
                "routing_format": "######",
                "webhook_events": ["payment.completed", "payment.failed", "transfer.completed", "refund.processed"]
            },
            "EU": {
                "name": "European Union Bank",
                "currencies": ["EUR", "GBP"],
                "account_format": "IBAN#########BIC#######",
                "routing_format": "BIC#######",
                "webhook_events": ["payment.completed", "payment.failed", "transfer.completed", "refund.processed"]
            },
            "UK": {
                "name": "British Financial Group",
                "currencies": ["GBP"],
                "account_format": "######-##########",
                "routing_format": "######",
                "webhook_events": ["payment.completed", "payment.failed", "transfer.completed", "refund.processed"]
            },
            "AU": {
                "name": "Australia Pacific Bank",
                "currencies": ["AUD"],
                "account_format": "####-####-####-###",
                "routing_format": "######",
                "webhook_events": ["payment.completed", "payment.failed", "transfer.completed", "refund.processed"]
            },
            "JP": {
                "name": "Japan International Bank",
                "currencies": ["JPY"],
                "account_format": "####-####-####-###",
                "routing_format": "######",
                "webhook_events": ["payment.completed", "payment.failed", "transfer.completed", "refund.processed"]
            }
        };

        // Transaction categories for realism
        const TRANSACTION_CATEGORIES = [
            "Groceries", "Restaurant", "Utilities", "Shopping", "Transportation",
            "Healthcare", "Entertainment", "Travel", "Education", "Insurance"
        ];

        // Merchant names for different regions
        const MERCHANTS = {
            "US": ["Walmart", "Amazon", "Starbucks", "Apple", "Target", "Best Buy", "Home Depot"],
            "EU": ["Carrefour", "Zalando", "Costa Coffee", "Samsung", "MediaMarkt", "IKEA"],
            "UK": ["Tesco", "ASOS", "Caffe Nero", "HSBC", "Barclays", "Boots"],
            "AU": ["Woolworths", "JB Hi-Fi", "Gloria Jean's", "Commonwealth Bank", "ANZ"],
            "JP": ["AEON", "Rakuten", "Doutor Coffee", "SoftBank", "Yodobashi Camera"]
        };

        // Store webhook history
        let webhooksSent = [];

        // DOM elements
        const webhookForm = document.getElementById('webhookForm');
        const sendEmailCheckbox = document.getElementById('send_email');
        const emailField = document.getElementById('emailField');
        const generateBtn = document.getElementById('generateBtn');
        const payloadDisplay = document.getElementById('payloadDisplay');
        const emailStatus = document.getElementById('emailStatus');
        const historyList = document.getElementById('historyList');
        const copyBtn = document.getElementById('copyBtn');

        // Event listeners
        sendEmailCheckbox.addEventListener('change', function() {
            emailField.style.display = this.checked ? 'block' : 'none';
        });

        generateBtn.addEventListener('click', generateWebhook);
        copyBtn.addEventListener('click', copyPayloadToClipboard);

        // Initialize
        loadHistory();

        // Function to generate a random transaction ID
        function generateTransactionId() {
            return 'txn_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        }

        // Function to generate a random webhook ID
        function generateWebhookId() {
            return 'wh_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
        }

        // Function to generate a random account number based on format
        function generateAccountNumber(countryCode) {
            const template = BANK_TEMPLATES[countryCode].account_format;
            let accountNumber = "";
            for (const char of template) {
                if (char === '#') {
                    accountNumber += Math.floor(Math.random() * 10);
                } else {
                    accountNumber += char;
                }
            }
            return accountNumber;
        }

        // Function to generate a random routing number based on format
        function generateRoutingNumber(countryCode) {
            const template = BANK_TEMPLATES[countryCode].routing_format;
            let routingNumber = "";
            for (const char of template) {
                if (char === '#') {
                    routingNumber += Math.floor(Math.random() * 10);
                } else {
                    routingNumber += char;
                }
            }
            return routingNumber;
        }

        // Function to generate a realistic amount based on currency
        function generateAmount(currency) {
            if (currency === "JPY") {
                return Math.floor(Math.random() * 50000) + 100;
            } else {
                return (Math.random() * 2000 + 5).toFixed(2);
            }
        }

        // Function to generate a payload
        function generatePayload() {
            const countryCode = document.getElementById('country').value;
            const eventType = document.getElementById('event_type').value;
            const apiVersion = document.getElementById('api_version').value;
            
            const bank = BANK_TEMPLATES[countryCode];
            const currency = bank.currencies[Math.floor(Math.random() * bank.currencies.length)];
            
            return {
                "id": generateWebhookId(),
                "event_type": eventType,
                "created_at": new Date().toISOString(),
                "api_version": apiVersion,
                "data": {
                    "object": {
                        "id": generateTransactionId(),
                        "amount": generateAmount(currency),
                        "currency": currency,
                        "status": eventType.includes('completed') ? "succeeded" : "failed",
                        "description": Payment to ${MERCHANTS[countryCode][Math.floor(Math.random() * MERCHANTS[countryCode].length)]},
                        "category": TRANSACTION_CATEGORIES[Math.floor(Math.random() * TRANSACTION_CATEGORIES.length)],
                        "merchant": {
                            "name": MERCHANTS[countryCode][Math.floor(Math.random() * MERCHANTS[countryCode].length)],
                            "country": countryCode
                        },
                        "source": {
                            "account_number": generateAccountNumber(countryCode),
                            "routing_number": generateRoutingNumber(countryCode),
                            "account_holder": {
                                "name": countryCode === "JP" ? "Tanaka Yamada" : "John Doe"
                            }
                        },
                        "destination": {
                            "account_number": generateAccountNumber(countryCode),
                            "routing_number": generateRoutingNumber(countryCode),
                            "account_holder": {
                                "name": countryCode === "JP" ? "Sato Hanako" : "Jane Smith"
                            }
                        },
                        "created": new Date().toISOString(),
                        "updated": new Date().toISOString()
                    }
                }
            };
        }

        // Function to simulate email sending
        async function sendEmail(email, payload) {
            // In a real implementation, you would make a request to your server
            // to send the email. This is a simulation.
            return new Promise((resolve) => {
                setTimeout(() => {
                    console.log(Simulating email sent to: ${email});
                    console.log('Payload:', payload);
                    resolve(true);
                }, 1000);
            });
        }

        // Function to generate webhook
        async function generateWebhook() {
            const sendEmail = document.getElementById('send_email').checked;
            const recipientEmail = document.getElementById('recipient_email').value;
            
            // Generate payload
            const payload = generatePayload();
            
            // Display payload
            payloadDisplay.textContent = JSON.stringify(payload, null, 2);
            
            // Send email if requested
            let emailStatusText = '';
            if (sendEmail && recipientEmail) {
                emailStatus.style.display = 'block';
                emailStatus.textContent = 'Sending email...';
                emailStatus.className = 'alert alert-info';
                
                try {
                    const success = await sendEmail(recipientEmail, payload);
                    if (success) {
                        emailStatusText = 'sent';
                        emailStatus.textContent = Webhook payload has been sent to ${recipientEmail};
                        emailStatus.className = 'alert alert-success';
                    } else {
                        emailStatusText = 'failed';
                        emailStatus.textContent = 'Failed to send email. Please check your email configuration.';
                        emailStatus.className = 'alert alert-danger';
                    }
                } catch (error) {
                    emailStatusText = 'failed';
                    emailStatus.textContent = 'Error sending email: ' + error.message;
                    emailStatus.className = 'alert alert-danger';
                }
            } else {
                emailStatus.style.display = 'none';
                emailStatusText = 'not_sent';
            }
            
            // Add to history
            webhooksSent.push({
                timestamp: new Date(),
                payload: payload,
                country: document.getElementById('country').value,
                event_type: document.getElementById('event_type').value,
                recipient_email: sendEmail ? recipientEmail : null,
                email_status: emailStatusText
            });
            
            // Save to localStorage
            saveHistory();
            
            // Update history display
            loadHistory();
        }

        // Function to copy payload to clipboard
        function copyPayloadToClipboard() {
            const payloadText = payloadDisplay.textContent;
            navigator.clipboard.writeText(payloadText).then(() => {
                // Show temporary feedback
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
            });
        }

        // Function to save history to localStorage
        function saveHistory() {
            const historyToSave = webhooksSent.slice(-10).map(wh => ({
                timestamp: wh.timestamp.toISOString(),
                payload: wh.payload,
                country: wh.country,
                event_type: wh.event_type,
                recipient_email: wh.recipient_email,
                email_status: wh.email_status
            }));
            
            localStorage.setItem('webhookHistory', JSON.stringify(historyToSave));
        }

        // Function to load history from localStorage
        function loadHistory() {
            const savedHistory = localStorage.getItem('webhookHistory');
            if (savedHistory) {
                try {
                    const history = JSON.parse(savedHistory);
                    historyList.innerHTML = '';
                    
                    history.forEach(item => {
                        const historyItem = document.createElement('div');
                        historyItem.className = 'history-item';
                        
                        const date = new Date(item.timestamp).toLocaleString();
                        const emailBadge = item.recipient_email ? 
                            <span class="badge bg-info email-badge">Emailed: ${item.recipient_email}</span> : '';
                        
                        historyItem.innerHTML = `
                            <strong>${item.event_type}</strong> (${item.country})
                            ${emailBadge}
                            <div class="text-muted small">${date}</div>
                        `;
                        
                        historyList.appendChild(historyItem);
                    });
                } catch (e) {
                    console.error('Error loading history:', e);
                    historyList.innerHTML = '<div class="text-muted">No history available</div>';
                }
            } else {
                historyList.innerHTML = '<div class="text-muted">No history available</div>';
            }
        }
    </script>
</body>
</html>